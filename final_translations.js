const fs = require('fs');

// 读取英文原文
const enData = JSON.parse(fs.readFileSync('src/locales/en.json', 'utf8'));

// 所有语言的基础翻译映射
const allTranslations = {
  es: {
    "Black Screen": "Pantalla Negra", "White Screen": "Pantalla Blanca", "Red Screen": "Pantalla Roja", "Green Screen": "Pantalla Verde", "Blue Screen": "Pantalla Azul", "Yellow Screen": "Pantalla Amarilla", "Orange Screen": "Pantalla Naranja", "Pink Screen": "Pantalla Rosa", "Purple Screen": "Pantalla Morada",
    "Black Screen Tool": "Herramienta de Pantalla Negra", "Blog": "Blog", "About Us": "Acerca de Nosotros", "Privacy Policy": "Política de Privacidad", "Terms of Service": "Términos de Servicio", "Contact Us": "Contáctanos", "All rights reserved.": "Todos los derechos reservados.",
    "Black": "Negro", "White": "Blanco", "Red": "Rojo", "Green": "Verde", "Blue": "Azul", "Yellow": "Amarillo", "Orange": "Naranja", "Pink": "Rosa", "Purple": "Morado",
    "Download Image": "Descargar Imagen", "Width": "Ancho", "Height": "Alto", "Start Timer": "Iniciar Temporizador", "Getting Started": "Comenzando"
  },
  fr: {
    "Black Screen": "Écran Noir", "White Screen": "Écran Blanc", "Red Screen": "Écran Rouge", "Green Screen": "Écran Vert", "Blue Screen": "Écran Bleu", "Yellow Screen": "Écran Jaune", "Orange Screen": "Écran Orange", "Pink Screen": "Écran Rose", "Purple Screen": "Écran Violet",
    "Black Screen Tool": "Outil Écran Noir", "Blog": "Blog", "About Us": "À Propos de Nous", "Privacy Policy": "Politique de Confidentialité", "Terms of Service": "Conditions d'Utilisation", "Contact Us": "Nous Contacter", "All rights reserved.": "Tous droits réservés.",
    "Black": "Noir", "White": "Blanc", "Red": "Rouge", "Green": "Vert", "Blue": "Bleu", "Yellow": "Jaune", "Orange": "Orange", "Pink": "Rose", "Purple": "Violet",
    "Download Image": "Télécharger l'Image", "Width": "Largeur", "Height": "Hauteur", "Start Timer": "Démarrer le Minuteur", "Getting Started": "Commencer"
  },
  it: {
    "Black Screen": "Schermo Nero", "White Screen": "Schermo Bianco", "Red Screen": "Schermo Rosso", "Green Screen": "Schermo Verde", "Blue Screen": "Schermo Blu", "Yellow Screen": "Schermo Giallo", "Orange Screen": "Schermo Arancione", "Pink Screen": "Schermo Rosa", "Purple Screen": "Schermo Viola",
    "Black Screen Tool": "Strumento Schermo Nero", "Blog": "Blog", "About Us": "Chi Siamo", "Privacy Policy": "Informativa sulla Privacy", "Terms of Service": "Termini di Servizio", "Contact Us": "Contattaci", "All rights reserved.": "Tutti i diritti riservati.",
    "Black": "Nero", "White": "Bianco", "Red": "Rosso", "Green": "Verde", "Blue": "Blu", "Yellow": "Giallo", "Orange": "Arancione", "Pink": "Rosa", "Purple": "Viola",
    "Download Image": "Scarica Immagine", "Width": "Larghezza", "Height": "Altezza", "Start Timer": "Avvia Timer", "Getting Started": "Iniziare"
  },
  pt: {
    "Black Screen": "Tela Preta", "White Screen": "Tela Branca", "Red Screen": "Tela Vermelha", "Green Screen": "Tela Verde", "Blue Screen": "Tela Azul", "Yellow Screen": "Tela Amarela", "Orange Screen": "Tela Laranja", "Pink Screen": "Tela Rosa", "Purple Screen": "Tela Roxa",
    "Black Screen Tool": "Ferramenta de Tela Preta", "Blog": "Blog", "About Us": "Sobre Nós", "Privacy Policy": "Política de Privacidade", "Terms of Service": "Termos de Serviço", "Contact Us": "Entre em Contato", "All rights reserved.": "Todos os direitos reservados.",
    "Black": "Preto", "White": "Branco", "Red": "Vermelho", "Green": "Verde", "Blue": "Azul", "Yellow": "Amarelo", "Orange": "Laranja", "Pink": "Rosa", "Purple": "Roxo",
    "Download Image": "Baixar Imagem", "Width": "Largura", "Height": "Altura", "Start Timer": "Iniciar Timer", "Getting Started": "Começando"
  },
  ja: {
    "Black Screen": "ブラックスクリーン", "White Screen": "ホワイトスクリーン", "Red Screen": "レッドスクリーン", "Green Screen": "グリーンスクリーン", "Blue Screen": "ブルースクリーン", "Yellow Screen": "イエロースクリーン", "Orange Screen": "オレンジスクリーン", "Pink Screen": "ピンクスクリーン", "Purple Screen": "パープルスクリーン",
    "Black Screen Tool": "ブラックスクリーンツール", "Blog": "ブログ", "About Us": "私たちについて", "Privacy Policy": "プライバシーポリシー", "Terms of Service": "利用規約", "Contact Us": "お問い合わせ", "All rights reserved.": "全著作権所有。",
    "Black": "黒", "White": "白", "Red": "赤", "Green": "緑", "Blue": "青", "Yellow": "黄", "Orange": "オレンジ", "Pink": "ピンク", "Purple": "紫",
    "Download Image": "画像をダウンロード", "Width": "幅", "Height": "高さ", "Start Timer": "タイマー開始", "Getting Started": "はじめに"
  },
  ko: {
    "Black Screen": "검은 화면", "White Screen": "흰 화면", "Red Screen": "빨간 화면", "Green Screen": "녹색 화면", "Blue Screen": "파란 화면", "Yellow Screen": "노란 화면", "Orange Screen": "주황 화면", "Pink Screen": "분홍 화면", "Purple Screen": "보라 화면",
    "Black Screen Tool": "검은 화면 도구", "Blog": "블로그", "About Us": "회사 소개", "Privacy Policy": "개인정보 보호정책", "Terms of Service": "서비스 약관", "Contact Us": "문의하기", "All rights reserved.": "모든 권리 보유.",
    "Black": "검은색", "White": "흰색", "Red": "빨간색", "Green": "녹색", "Blue": "파란색", "Yellow": "노란색", "Orange": "주황색", "Pink": "분홍색", "Purple": "보라색",
    "Download Image": "이미지 다운로드", "Width": "너비", "Height": "높이", "Start Timer": "타이머 시작", "Getting Started": "시작하기"
  },
  tr: {
    "Black Screen": "Siyah Ekran", "White Screen": "Beyaz Ekran", "Red Screen": "Kırmızı Ekran", "Green Screen": "Yeşil Ekran", "Blue Screen": "Mavi Ekran", "Yellow Screen": "Sarı Ekran", "Orange Screen": "Turuncu Ekran", "Pink Screen": "Pembe Ekran", "Purple Screen": "Mor Ekran",
    "Black Screen Tool": "Siyah Ekran Aracı", "Blog": "Blog", "About Us": "Hakkımızda", "Privacy Policy": "Gizlilik Politikası", "Terms of Service": "Hizmet Şartları", "Contact Us": "İletişim", "All rights reserved.": "Tüm hakları saklıdır.",
    "Black": "Siyah", "White": "Beyaz", "Red": "Kırmızı", "Green": "Yeşil", "Blue": "Mavi", "Yellow": "Sarı", "Orange": "Turuncu", "Pink": "Pembe", "Purple": "Mor",
    "Download Image": "Resmi İndir", "Width": "Genişlik", "Height": "Yükseklik", "Start Timer": "Zamanlayıcıyı Başlat", "Getting Started": "Başlarken"
  },
  hk: {
    "Black Screen": "黑屏", "White Screen": "白屏", "Red Screen": "紅屏", "Green Screen": "綠屏", "Blue Screen": "藍屏", "Yellow Screen": "黃屏", "Orange Screen": "橙屏", "Pink Screen": "粉屏", "Purple Screen": "紫屏",
    "Black Screen Tool": "黑屏工具", "Blog": "網誌", "About Us": "關於我們", "Privacy Policy": "私隱政策", "Terms of Service": "服務條款", "Contact Us": "聯絡我們", "All rights reserved.": "版權所有。",
    "Black": "黑色", "White": "白色", "Red": "紅色", "Green": "綠色", "Blue": "藍色", "Yellow": "黃色", "Orange": "橙色", "Pink": "粉色", "Purple": "紫色",
    "Download Image": "下載圖像", "Width": "寬度", "Height": "高度", "Start Timer": "開始計時器", "Getting Started": "開始使用"
  }
};

// 为每种语言生成翻译文件
Object.keys(allTranslations).forEach(lang => {
  const langTranslations = {};
  const langMap = allTranslations[lang];
  
  Object.keys(enData).forEach(key => {
    if (langMap[key]) {
      langTranslations[key] = langMap[key];
    } else {
      // 如果没有特定翻译，保持英文原文
      langTranslations[key] = enData[key];
    }
  });
  
  // 写入文件
  fs.writeFileSync(`src/locales/${lang}.json`, JSON.stringify(langTranslations, null, 4));
  console.log(`Generated ${lang}.json with ${Object.keys(langTranslations).length} entries (${Object.keys(langMap).length} translated)`);
});

console.log('All translation files generated successfully!');
