const fs = require('fs');

// 读取英文原文
const enData = JSON.parse(fs.readFileSync('src/locales/en.json', 'utf8'));

// 定义各语言的基础翻译映射
const translations = {
  es: {
    "Black Screen": "Pantalla Negra",
    "White Screen": "Pantalla Blanca",
    "Red Screen": "Pantalla Roja",
    "Green Screen": "Pantalla Verde",
    "Blue Screen": "Pantalla Azul",
    "Yellow Screen": "Pantalla Amarilla",
    "Orange Screen": "Pantalla Naranja",
    "Pink Screen": "Pantalla Rosa",
    "Purple Screen": "Pantalla Morada",
    "Black Screen Tool": "Herramienta de Pantalla Negra",
    "White Screen Tool": "Herramienta de Pantalla Blanca",
    "Red Screen Tool": "Herramienta de Pantalla Roja",
    "Green Screen Tool": "Herramienta de Pantalla Verde",
    "Blue Screen Tool": "Herramienta de Pantalla Azul",
    "Yellow Screen Tool": "Herramienta de Pantalla Amarilla",
    "Orange Screen Tool": "Herramienta de Pantalla Naranja",
    "Pink Screen Tool": "Herramienta de Pantalla Rosa",
    "Purple Screen Tool": "Herramienta de Pantalla Morada",
    "Blog": "Blog",
    "About Us": "Acerca de Nosotros",
    "Privacy Policy": "Política de Privacidad",
    "Terms of Service": "Términos de Servicio",
    "Quick Links": "Enlaces Rápidos",
    "Friends Link": "Enlaces de Amigos",
    "Contact Us": "Contáctanos",
    "All rights reserved.": "Todos los derechos reservados.",
    "Black": "Negro",
    "White": "Blanco",
    "Red": "Rojo",
    "Green": "Verde",
    "Blue": "Azul",
    "Yellow": "Amarillo",
    "Orange": "Naranja",
    "Pink": "Rosa",
    "Purple": "Morado",
    "Screen Preview": "Vista Previa de Pantalla",
    "Press F for fullscreen, ESC to exit, SPACE to toggle": "Presiona F para pantalla completa, ESC para salir, ESPACIO para alternar",
    "Color Selection": "Selección de Color",
    "Download Image": "Descargar Imagen",
    "Width": "Ancho",
    "Height": "Alto",
    "Timer Settings": "Configuración del Temporizador",
    "Start Timer": "Iniciar Temporizador",
    "OLED Protection Settings": "Configuración de Protección OLED",
    "Start OLED Protection": "Iniciar Protección OLED",
    "Getting Started": "Comenzando",
    "How to Use Black Screen Tool": "Cómo Usar la Herramienta de Pantalla Negra",
    "Software Developer": "Desarrollador de Software",
    "Graphic Designer": "Diseñador Gráfico",
    "Gamer": "Jugador",
    "Teacher": "Profesor",
    "IT Support": "Soporte de TI",
    "Student": "Estudiante"
  },
  fr: {
    "Black Screen": "Écran Noir",
    "White Screen": "Écran Blanc",
    "Red Screen": "Écran Rouge",
    "Green Screen": "Écran Vert",
    "Blue Screen": "Écran Bleu",
    "Yellow Screen": "Écran Jaune",
    "Orange Screen": "Écran Orange",
    "Pink Screen": "Écran Rose",
    "Purple Screen": "Écran Violet",
    "Black Screen Tool": "Outil Écran Noir",
    "White Screen Tool": "Outil Écran Blanc",
    "Red Screen Tool": "Outil Écran Rouge",
    "Green Screen Tool": "Outil Écran Vert",
    "Blue Screen Tool": "Outil Écran Bleu",
    "Yellow Screen Tool": "Outil Écran Jaune",
    "Orange Screen Tool": "Outil Écran Orange",
    "Pink Screen Tool": "Outil Écran Rose",
    "Purple Screen Tool": "Outil Écran Violet",
    "Blog": "Blog",
    "About Us": "À Propos de Nous",
    "Privacy Policy": "Politique de Confidentialité",
    "Terms of Service": "Conditions d'Utilisation",
    "Quick Links": "Liens Rapides",
    "Friends Link": "Liens d'Amis",
    "Contact Us": "Nous Contacter",
    "All rights reserved.": "Tous droits réservés.",
    "Black": "Noir",
    "White": "Blanc",
    "Red": "Rouge",
    "Green": "Vert",
    "Blue": "Bleu",
    "Yellow": "Jaune",
    "Orange": "Orange",
    "Pink": "Rose",
    "Purple": "Violet",
    "Screen Preview": "Aperçu de l'Écran",
    "Press F for fullscreen, ESC to exit, SPACE to toggle": "Appuyez sur F pour plein écran, ÉCHAP pour quitter, ESPACE pour basculer",
    "Color Selection": "Sélection de Couleur",
    "Download Image": "Télécharger l'Image",
    "Width": "Largeur",
    "Height": "Hauteur",
    "Timer Settings": "Paramètres du Minuteur",
    "Start Timer": "Démarrer le Minuteur",
    "OLED Protection Settings": "Paramètres de Protection OLED",
    "Start OLED Protection": "Démarrer la Protection OLED",
    "Getting Started": "Commencer",
    "How to Use Black Screen Tool": "Comment Utiliser l'Outil Écran Noir",
    "Software Developer": "Développeur de Logiciels",
    "Graphic Designer": "Graphiste",
    "Gamer": "Joueur",
    "Teacher": "Enseignant",
    "IT Support": "Support Informatique",
    "Student": "Étudiant"
  },
  it: {
    "Black Screen": "Schermo Nero",
    "White Screen": "Schermo Bianco",
    "Red Screen": "Schermo Rosso",
    "Green Screen": "Schermo Verde",
    "Blue Screen": "Schermo Blu",
    "Yellow Screen": "Schermo Giallo",
    "Orange Screen": "Schermo Arancione",
    "Pink Screen": "Schermo Rosa",
    "Purple Screen": "Schermo Viola",
    "Black Screen Tool": "Strumento Schermo Nero",
    "White Screen Tool": "Strumento Schermo Bianco",
    "Red Screen Tool": "Strumento Schermo Rosso",
    "Green Screen Tool": "Strumento Schermo Verde",
    "Blue Screen Tool": "Strumento Schermo Blu",
    "Yellow Screen Tool": "Strumento Schermo Giallo",
    "Orange Screen Tool": "Strumento Schermo Arancione",
    "Pink Screen Tool": "Strumento Schermo Rosa",
    "Purple Screen Tool": "Strumento Schermo Viola",
    "Blog": "Blog",
    "About Us": "Chi Siamo",
    "Privacy Policy": "Informativa sulla Privacy",
    "Terms of Service": "Termini di Servizio",
    "Quick Links": "Collegamenti Rapidi",
    "Friends Link": "Collegamenti Amici",
    "Contact Us": "Contattaci",
    "All rights reserved.": "Tutti i diritti riservati.",
    "Black": "Nero",
    "White": "Bianco",
    "Red": "Rosso",
    "Green": "Verde",
    "Blue": "Blu",
    "Yellow": "Giallo",
    "Orange": "Arancione",
    "Pink": "Rosa",
    "Purple": "Viola",
    "Screen Preview": "Anteprima Schermo",
    "Press F for fullscreen, ESC to exit, SPACE to toggle": "Premi F per schermo intero, ESC per uscire, SPAZIO per alternare",
    "Color Selection": "Selezione Colore",
    "Download Image": "Scarica Immagine",
    "Width": "Larghezza",
    "Height": "Altezza",
    "Timer Settings": "Impostazioni Timer",
    "Start Timer": "Avvia Timer",
    "OLED Protection Settings": "Impostazioni Protezione OLED",
    "Start OLED Protection": "Avvia Protezione OLED",
    "Getting Started": "Iniziare",
    "How to Use Black Screen Tool": "Come Usare lo Strumento Schermo Nero",
    "Software Developer": "Sviluppatore Software",
    "Graphic Designer": "Graphic Designer",
    "Gamer": "Giocatore",
    "Teacher": "Insegnante",
    "IT Support": "Supporto IT",
    "Student": "Studente"
  },
  pt: {
    "Black Screen": "Tela Preta",
    "White Screen": "Tela Branca",
    "Red Screen": "Tela Vermelha",
    "Green Screen": "Tela Verde",
    "Blue Screen": "Tela Azul",
    "Yellow Screen": "Tela Amarela",
    "Orange Screen": "Tela Laranja",
    "Pink Screen": "Tela Rosa",
    "Purple Screen": "Tela Roxa",
    "Black Screen Tool": "Ferramenta de Tela Preta",
    "Blog": "Blog",
    "About Us": "Sobre Nós",
    "Privacy Policy": "Política de Privacidade",
    "Contact Us": "Entre em Contato",
    "All rights reserved.": "Todos os direitos reservados.",
    "Black": "Preto",
    "White": "Branco",
    "Red": "Vermelho",
    "Green": "Verde",
    "Blue": "Azul",
    "Yellow": "Amarelo",
    "Orange": "Laranja",
    "Pink": "Rosa",
    "Purple": "Roxo"
  },
  ja: {
    "Black Screen": "ブラックスクリーン",
    "White Screen": "ホワイトスクリーン",
    "Red Screen": "レッドスクリーン",
    "Green Screen": "グリーンスクリーン",
    "Blue Screen": "ブルースクリーン",
    "Yellow Screen": "イエロースクリーン",
    "Orange Screen": "オレンジスクリーン",
    "Pink Screen": "ピンクスクリーン",
    "Purple Screen": "パープルスクリーン",
    "Black Screen Tool": "ブラックスクリーンツール",
    "Blog": "ブログ",
    "About Us": "私たちについて",
    "Privacy Policy": "プライバシーポリシー",
    "Contact Us": "お問い合わせ",
    "All rights reserved.": "全著作権所有。",
    "Black": "黒",
    "White": "白",
    "Red": "赤",
    "Green": "緑",
    "Blue": "青",
    "Yellow": "黄",
    "Orange": "オレンジ",
    "Pink": "ピンク",
    "Purple": "紫"
  },
  ko: {
    "Black Screen": "검은 화면",
    "White Screen": "흰 화면",
    "Red Screen": "빨간 화면",
    "Green Screen": "녹색 화면",
    "Blue Screen": "파란 화면",
    "Yellow Screen": "노란 화면",
    "Orange Screen": "주황 화면",
    "Pink Screen": "분홍 화면",
    "Purple Screen": "보라 화면",
    "Black Screen Tool": "검은 화면 도구",
    "Blog": "블로그",
    "About Us": "회사 소개",
    "Privacy Policy": "개인정보 보호정책",
    "Contact Us": "문의하기",
    "All rights reserved.": "모든 권리 보유.",
    "Black": "검은색",
    "White": "흰색",
    "Red": "빨간색",
    "Green": "녹색",
    "Blue": "파란색",
    "Yellow": "노란색",
    "Orange": "주황색",
    "Pink": "분홍색",
    "Purple": "보라색"
  },
  tr: {
    "Black Screen": "Siyah Ekran",
    "White Screen": "Beyaz Ekran",
    "Red Screen": "Kırmızı Ekran",
    "Green Screen": "Yeşil Ekran",
    "Blue Screen": "Mavi Ekran",
    "Yellow Screen": "Sarı Ekran",
    "Orange Screen": "Turuncu Ekran",
    "Pink Screen": "Pembe Ekran",
    "Purple Screen": "Mor Ekran",
    "Black Screen Tool": "Siyah Ekran Aracı",
    "Blog": "Blog",
    "About Us": "Hakkımızda",
    "Privacy Policy": "Gizlilik Politikası",
    "Contact Us": "İletişim",
    "All rights reserved.": "Tüm hakları saklıdır.",
    "Black": "Siyah",
    "White": "Beyaz",
    "Red": "Kırmızı",
    "Green": "Yeşil",
    "Blue": "Mavi",
    "Yellow": "Sarı",
    "Orange": "Turuncu",
    "Pink": "Pembe",
    "Purple": "Mor"
  },
  hk: {
    "Black Screen": "黑屏",
    "White Screen": "白屏",
    "Red Screen": "紅屏",
    "Green Screen": "綠屏",
    "Blue Screen": "藍屏",
    "Yellow Screen": "黃屏",
    "Orange Screen": "橙屏",
    "Pink Screen": "粉屏",
    "Purple Screen": "紫屏",
    "Black Screen Tool": "黑屏工具",
    "Blog": "網誌",
    "About Us": "關於我們",
    "Privacy Policy": "私隱政策",
    "Contact Us": "聯絡我們",
    "All rights reserved.": "版權所有。",
    "Black": "黑色",
    "White": "白色",
    "Red": "紅色",
    "Green": "綠色",
    "Blue": "藍色",
    "Yellow": "黃色",
    "Orange": "橙色",
    "Pink": "粉色",
    "Purple": "紫色"
  }
};

// 为每种语言生成翻译文件
Object.keys(translations).forEach(lang => {
  const langTranslations = {};
  const langMap = translations[lang];

  Object.keys(enData).forEach(key => {
    if (langMap[key]) {
      langTranslations[key] = langMap[key];
    } else {
      // 如果没有特定翻译，保持英文原文
      langTranslations[key] = enData[key];
    }
  });

  // 写入文件
  fs.writeFileSync(`src/locales/${lang}.json`, JSON.stringify(langTranslations, null, 4));
  console.log(`Generated ${lang}.json with ${Object.keys(langTranslations).length} entries`);
});

console.log('All translation files generated successfully!');
